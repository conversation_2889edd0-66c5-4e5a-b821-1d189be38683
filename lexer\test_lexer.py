"""
Test script for the C Lexer
Demonstrates the lexer functionality with sample C code.
"""

from c_lexer import <PERSON><PERSON><PERSON><PERSON>

def test_basic_c_program():
    """Test lexer with a basic C program."""
    
    sample_code = '''#include <stdio.h>

int main() {
    int a = 5;
    int b = 10;
    int sum = a + b;
    printf("Sum: %d", sum);
    return 0;
}'''
    
    print("Testing Basic C Program:")
    print("=" * 40)
    print("Input Code:")
    print(sample_code)
    print("\n" + "=" * 40)
    
    lexer = CLexer()
    result = lexer.get_formatted_output(sample_code)
    print(result)

def test_complex_c_program():
    """Test lexer with a more complex C program."""
    
    complex_code = '''#include <stdio.h>
#include <stdlib.h>

// Function to calculate factorial
int factorial(int n) {
    if (n <= 1) {
        return 1;
    }
    return n * factorial(n - 1);
}

int main() {
    int num = 5;
    float result;
    char message[] = "Factorial of";
    
    /* Calculate factorial */
    result = factorial(num);
    
    printf("%s %d is %.0f\\n", message, num, result);
    
    // Loop example
    for (int i = 0; i < 3; i++) {
        printf("Iteration: %d\\n", i);
    }
    
    return 0;
}'''
    
    print("\n\nTesting Complex C Program:")
    print("=" * 40)
    print("Input Code:")
    print(complex_code)
    print("\n" + "=" * 40)
    
    lexer = CLexer()
    result = lexer.get_formatted_output(complex_code)
    print(result)

def test_operators_and_expressions():
    """Test lexer with various operators and expressions."""
    
    operators_code = '''int main() {
    int a = 10, b = 5;
    int result;
    
    // Arithmetic operators
    result = a + b;
    result = a - b;
    result = a * b;
    result = a / b;
    result = a % b;
    
    // Assignment operators
    a += b;
    a -= b;
    a *= b;
    a /= b;
    
    // Comparison operators
    if (a == b) result = 1;
    if (a != b) result = 2;
    if (a < b) result = 3;
    if (a > b) result = 4;
    if (a <= b) result = 5;
    if (a >= b) result = 6;
    
    // Logical operators
    if (a && b) result = 7;
    if (a || b) result = 8;
    if (!a) result = 9;
    
    // Increment/Decrement
    a++;
    b--;
    ++a;
    --b;
    
    return 0;
}'''
    
    print("\n\nTesting Operators and Expressions:")
    print("=" * 40)
    print("Input Code:")
    print(operators_code)
    print("\n" + "=" * 40)
    
    lexer = CLexer()
    result = lexer.get_formatted_output(operators_code)
    print(result)

if __name__ == "__main__":
    test_basic_c_program()
    test_complex_c_program()
    test_operators_and_expressions()
