"""
Main module for C Lexer
Provides a simple interface to analyze C code using the lexer.
"""

import sys
import os
from c_lexer import <PERSON><PERSON><PERSON><PERSON>

def analyze_c_code(code):
    """
    Analyze C code and return formatted lexical analysis results.
    
    Args:
        code (str): C source code to analyze
        
    Returns:
        str: Formatted lexical analysis output
    """
    try:
        lexer = CLexer()
        return lexer.get_formatted_output(code)
    except Exception as e:
        return f"Error during lexical analysis: {str(e)}"

def main():
    """
    Main function for command-line usage.
    """
    if len(sys.argv) < 2:
        print("Usage: python main.py <c_code_file>")
        print("Or provide C code as a string argument")
        return
    
    # Check if argument is a file path or direct code
    input_arg = sys.argv[1]
    
    if os.path.isfile(input_arg):
        # Read from file
        try:
            with open(input_arg, 'r') as file:
                code = file.read()
            print(f"Analyzing file: {input_arg}")
            print("=" * 50)
        except Exception as e:
            print(f"Error reading file: {e}")
            return
    else:
        # Treat as direct code input
        code = input_arg
        print("Analyzing provided code:")
        print("=" * 30)
    
    # Perform lexical analysis
    result = analyze_c_code(code)
    print(result)

if __name__ == "__main__":
    main()
