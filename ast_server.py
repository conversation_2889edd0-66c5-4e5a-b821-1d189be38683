"""
Simple AST Server
Standalone server for generating AST from C code.
"""

import json
import sys
from http.server import HTTPServer, BaseHTTPRequestHandler

# Add paths for imports
sys.path.append('.')
sys.path.append('parser')
sys.path.append('lexer')

from lexer.c_lexer import <PERSON><PERSON><PERSON><PERSON>
from parser.ast_nodes import *
from parser.c_parser import CParser
from parser.ast_formatter import format_ast_simple

class ASTHandler(BaseHTTPRequestHandler):
    """HTTP request handler for AST API."""
    
    def do_POST(self):
        """Handle POST requests."""
        if self.path == '/parse':
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                data = json.loads(post_data.decode('utf-8'))
                code = data.get('code', '')
                
                if code:
                    result = self.parse_code(code)
                    self.send_json_response(result)
                else:
                    self.send_error_response("No code provided")
            
            except json.JSONDecodeError:
                self.send_error_response("Invalid JSON data")
            except Exception as e:
                self.send_error_response(f"Server error: {str(e)}")
        else:
            self.send_404()
    
    def parse_code(self, code):
        """Parse C code and return AST results."""
        try:
            print(f"Parsing code: {code[:50]}...")  # Debug output
            
            parser = CParser()
            ast = parser.parse(code)
            formatted_output = format_ast_simple(ast)
            
            print("AST generated successfully")  # Debug output
            
            return {
                'success': True,
                'formatted_output': formatted_output
            }
        
        except Exception as e:
            print(f"Error during parsing: {e}")  # Debug output
            import traceback
            traceback.print_exc()
            
            return {
                'success': False,
                'error': str(e),
                'formatted_output': f"Error during AST generation: {str(e)}"
            }
    
    def send_json_response(self, data):
        """Send JSON response."""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        json_data = json.dumps(data, indent=2)
        self.wfile.write(json_data.encode('utf-8'))
    
    def send_error_response(self, message):
        """Send error response."""
        error_data = {
            'success': False,
            'error': message,
            'formatted_output': f"Error: {message}"
        }
        self.send_json_response(error_data)
    
    def send_404(self):
        """Send 404 response."""
        self.send_response(404)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        self.wfile.write(b'404 Not Found')
    
    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS."""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

def run_server(port=8002):
    """Run the AST HTTP server."""
    server_address = ('', port)
    httpd = HTTPServer(server_address, ASTHandler)
    print(f"Starting AST server on port {port}")
    print(f"API endpoint: http://localhost:{port}/parse")
    print("Press Ctrl+C to stop the server")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped.")
        httpd.server_close()

if __name__ == "__main__":
    port = 8002
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("Invalid port number. Using default port 8002.")
    
    run_server(port)
