# C Code Analyzer

A web-based C code analyzer with lexical analysis and AST generation.

## Quick Start

1. **Start the lexer server:**
   ```bash
   cd lexer
   python web_interface.py 8001
   ```

2. **Start the parser server:**
   ```bash
   cd parser
   python web_interface.py 8002
   ```

3. **Open the frontend:**
   Open `frontend/index.html` in your web browser

4. **Analyze C code:**
   - Enter your C code in the textarea
   - Select analysis type (Lexical Analysis or AST)
   - Click "Analyze Code"
   - View detailed results

## Project Structure

```
C-Code-Analyzer/
├── frontend/
│   ├── index.html          # Main web interface
│   ├── script.js           # Frontend logic
│   └── style.css           # Styling
├── lexer/
│   ├── c_lexer.py          # PLY-based C lexer
│   ├── web_interface.py    # Lexer HTTP API server
│   └── __init__.py         # Package file
├── parser/
│   ├── ast_nodes.py        # AST node definitions
│   ├── c_parser.py         # C language parser
│   ├── ast_formatter.py    # AST formatting utilities
│   ├── web_interface.py    # Parser HTTP API server
│   └── __init__.py         # Package file
└── README.md               # This file
```

## Requirements

- Python 3.6+
- PLY library: `pip install ply`

## Features

- **Lexical Analysis**: Complete C language tokenization
- **AST Generation**: Abstract Syntax Tree creation and visualization
- **Token identification**: Keywords, operators, literals, etc.
- **Tree structure**: Hierarchical code representation
- **Clean web interface**: Easy-to-use frontend
- **Real-time analysis**: Instant results

## API Endpoints

- **Lexer**: `http://localhost:8001/analyze`
- **Parser**: `http://localhost:8002/parse`

Simple and functional!
