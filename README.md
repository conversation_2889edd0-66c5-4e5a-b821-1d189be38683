# C Code Analyzer

A simple web-based C code lexical analyzer.

## Quick Start

1. **Start the lexer server:**
   ```bash
   cd lexer
   python web_interface.py 8001
   ```

2. **Open the frontend:**
   Open `frontend/index.html` in your web browser

3. **Analyze C code:**
   - Enter your C code in the textarea
   - Click "Analyze Code"
   - View detailed lexical analysis results

## Project Structure

```
C-Code-Analyzer/
├── frontend/
│   ├── index.html          # Main web interface
│   ├── script.js           # Frontend logic
│   └── style.css           # Styling
├── lexer/
│   ├── c_lexer.py          # PLY-based C lexer
│   ├── web_interface.py    # HTTP API server
│   └── __init__.py         # Package file
└── README.md               # This file
```

## Requirements

- Python 3.6+
- PLY library: `pip install ply`

## Features

- Complete C language lexical analysis
- Token identification (keywords, operators, literals, etc.)
- Line-by-line token breakdown
- Clean web interface
- Real-time analysis

That's it! Simple and functional.
