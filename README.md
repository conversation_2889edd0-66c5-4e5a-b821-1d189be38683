# C Code Analyzer

A comprehensive web-based C code analysis tool that provides lexical analysis, AST generation, and flowchart visualization.

## Features

- **Lexical Analysis**: Complete tokenization of C code using PLY (Python Lex-Yacc)
- **AST Generation**: Abstract Syntax Tree visualization (placeholder)
- **Flowchart**: Visual program flow representation (placeholder)
- **Web Interface**: Clean, responsive frontend for easy code analysis
- **Real-time Analysis**: Instant feedback as you type and analyze code

## Project Structure

```
C-Code-Analyzer/
├── frontend/                 # Web frontend
│   ├── index.html           # Main HTML page
│   ├── script.js            # JavaScript functionality
│   └── style.css            # Styling
├── lexer/                   # Lexical analysis engine
│   ├── c_lexer.py          # Main lexer implementation
│   ├── main.py             # Command-line interface
│   ├── web_interface.py    # HTTP API server
│   ├── test_lexer.py       # Test suite
│   └── README.md           # Lexer documentation
├── start_analyzer.py       # Easy startup script
└── README.md              # This file
```

## Quick Start

### Option 1: Easy Startup (Recommended)

```bash
python start_analyzer.py
```

This will:
- Start the lexer server on port 8001
- Open the frontend in your default browser
- Display status information

### Option 2: Manual Setup

1. **Start the lexer server:**
   ```bash
   cd lexer
   python web_interface.py 8001
   ```

2. **Open the frontend:**
   Open `frontend/index.html` in your web browser

## Requirements

- Python 3.6+
- PLY (Python Lex-Yacc)

### Installation

```bash
pip install ply
```

## Usage

1. **Start the application** using one of the methods above
2. **Enter C code** in the input textarea
3. **Select analysis type** from the dropdown:
   - **Lexical Analysis**: Detailed token breakdown
   - **AST**: Abstract Syntax Tree (placeholder)
   - **Flowchart**: Program flow visualization (placeholder)
4. **Click "Analyze Code"** to see results

### Example C Code

```c
#include <stdio.h>

int main() {
    int a = 5;
    int b = 10;
    int sum = a + b;
    printf("Sum: %d", sum);
    return 0;
}
```

## Lexical Analysis Features

The lexer recognizes all standard C language elements:

- **Keywords**: `int`, `float`, `if`, `while`, `return`, etc.
- **Operators**: `+`, `-`, `*`, `/`, `=`, `==`, `!=`, `&&`, `||`, etc.
- **Literals**: Numbers, strings, characters
- **Identifiers**: Variable and function names
- **Delimiters**: `()`, `{}`, `[]`, `;`, `,`, etc.
- **Comments**: Single-line (`//`) and multi-line (`/* */`)
- **Preprocessor**: `#include`, `#define`, etc.

## API Usage

The lexer also provides an HTTP API:

```bash
# POST request
curl -X POST http://localhost:8001/analyze \
  -H "Content-Type: application/json" \
  -d '{"code": "int main() { return 0; }"}'

# GET request
curl "http://localhost:8001/analyze?code=int main() { return 0; }"
```

## Development

### Running Tests

```bash
cd lexer
python test_lexer.py
```

### Command Line Usage

```bash
cd lexer
python main.py "int main() { return 0; }"
# or
python main.py sample.c
```

## Architecture

- **Frontend**: Pure HTML/CSS/JavaScript with responsive design
- **Backend**: Python-based lexer using PLY
- **Communication**: HTTP API with JSON responses
- **Fallback**: Client-side basic analysis when server unavailable

## Future Enhancements

- [ ] Complete AST generation implementation
- [ ] Interactive flowchart generation
- [ ] Syntax error highlighting
- [ ] Code formatting and beautification
- [ ] Export functionality (PDF, image)
- [ ] Multiple language support

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is open source and available under the MIT License.
