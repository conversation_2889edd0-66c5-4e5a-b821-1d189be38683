#!/usr/bin/env python3
"""
Startup script for C Code Analyzer
Starts the lexer server and opens the frontend in the browser.
"""

import subprocess
import time
import webbrowser
import os
import sys
from pathlib import Path

def start_lexer_server(port=8001):
    """Start the lexer server in the background."""
    lexer_path = Path(__file__).parent / "lexer" / "web_interface.py"
    
    if not lexer_path.exists():
        print(f"Error: Lexer server not found at {lexer_path}")
        return None
    
    try:
        print(f"Starting lexer server on port {port}...")
        process = subprocess.Popen([
            sys.executable, str(lexer_path), str(port)
        ], cwd=lexer_path.parent)
        
        # Give the server time to start
        time.sleep(2)
        
        print(f"✓ Lexer server started on http://localhost:{port}")
        return process
    
    except Exception as e:
        print(f"Error starting lexer server: {e}")
        return None

def open_frontend():
    """Open the frontend in the default browser."""
    frontend_path = Path(__file__).parent / "frontend" / "index.html"
    
    if not frontend_path.exists():
        print(f"Error: Frontend not found at {frontend_path}")
        return False
    
    try:
        frontend_url = f"file:///{frontend_path.absolute().as_posix()}"
        print(f"Opening frontend: {frontend_url}")
        webbrowser.open(frontend_url)
        print("✓ Frontend opened in browser")
        return True
    
    except Exception as e:
        print(f"Error opening frontend: {e}")
        return False

def main():
    """Main function to start the C Code Analyzer."""
    print("=" * 50)
    print("C Code Analyzer Startup")
    print("=" * 50)
    
    # Start lexer server
    server_process = start_lexer_server()
    if not server_process:
        print("Failed to start lexer server. Exiting.")
        return 1
    
    # Open frontend
    if not open_frontend():
        print("Failed to open frontend.")
        server_process.terminate()
        return 1
    
    print("\n" + "=" * 50)
    print("C Code Analyzer is now running!")
    print("=" * 50)
    print("• Lexer server: http://localhost:8001")
    print("• Frontend: Opened in your default browser")
    print("• Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        # Keep the script running and monitor the server
        server_process.wait()
    except KeyboardInterrupt:
        print("\n\nShutting down...")
        server_process.terminate()
        server_process.wait()
        print("✓ Server stopped")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
