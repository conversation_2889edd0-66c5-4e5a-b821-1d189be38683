# C Language Lexer

A comprehensive lexical analyzer for the C programming language implemented using PLY (Python Lex-Yacc).

## Features

- **Complete C Token Support**: Recognizes all C language tokens including:
  - Keywords (int, float, if, while, etc.)
  - Identifiers and literals
  - Operators (arithmetic, logical, bitwise, assignment)
  - Delimiters and punctuation
  - Comments (single-line and multi-line)
  - Preprocessor directives

- **Detailed Analysis**: Provides comprehensive token information including:
  - Token type and value
  - Line number and position
  - Token count summary
  - Formatted output for easy reading

- **Multiple Interfaces**: 
  - Command-line interface
  - Python module import
  - HTTP API server
  - Test suite

## Installation

Make sure you have PLY installed:

```bash
pip install ply
```

## Usage

### 1. Command Line Interface

```bash
# Analyze a C file
python main.py sample.c

# Analyze code directly
python main.py "int main() { return 0; }"
```

### 2. Python Module

```python
from c_lexer import CLexer

# Create lexer instance
lexer = CLexer()

# Analyze C code
code = '''
#include <stdio.h>
int main() {
    printf("Hello, World!");
    return 0;
}
'''

# Get formatted output
result = lexer.get_formatted_output(code)
print(result)

# Get token list
tokens = lexer.tokenize(code)
for token in tokens:
    print(f"{token['type']}: {token['value']}")
```

### 3. HTTP API Server

Start the web server:

```bash
python web_interface.py [port]
```

Then use the API:

```bash
# GET request
curl "http://localhost:8000/analyze?code=int main() { return 0; }"

# POST request
curl -X POST http://localhost:8000/analyze \
  -H "Content-Type: application/json" \
  -d '{"code": "int main() { return 0; }"}'
```

### 4. Running Tests

```bash
python test_lexer.py
```

## Supported C Language Features

### Keywords
- Data types: `int`, `float`, `double`, `char`, `void`
- Control flow: `if`, `else`, `while`, `for`, `do`, `switch`, `case`, `default`
- Storage classes: `auto`, `static`, `extern`, `register`
- Other: `return`, `break`, `continue`, `goto`, `sizeof`, `typedef`, `struct`, `union`, `enum`

### Operators
- Arithmetic: `+`, `-`, `*`, `/`, `%`
- Assignment: `=`, `+=`, `-=`, `*=`, `/=`, `%=`
- Comparison: `==`, `!=`, `<`, `>`, `<=`, `>=`
- Logical: `&&`, `||`, `!`
- Bitwise: `&`, `|`, `^`, `~`, `<<`, `>>`
- Increment/Decrement: `++`, `--`

### Literals
- Integer literals: `123`, `0x1A`, `077`
- Float literals: `3.14`, `2.5f`, `1.0e-5`
- Character literals: `'a'`, `'\n'`, `'\0'`
- String literals: `"Hello, World!"`

### Delimiters
- Parentheses: `(`, `)`
- Braces: `{`, `}`
- Brackets: `[`, `]`
- Punctuation: `;`, `,`, `.`, `->`

### Comments
- Single-line: `// comment`
- Multi-line: `/* comment */`

### Preprocessor
- Directives: `#include`, `#define`, `#ifdef`, `#ifndef`, `#endif`
- Header files: `<stdio.h>`, `<stdlib.h>`

## Output Format

The lexer provides detailed analysis in the following format:

```
Lexical Analysis Results:
==================================================

Token Summary:
--------------------
IDENTIFIER: 5
INT: 2
SEMICOLON: 3
...

Detailed Token List:
------------------------------
Line   Type                 Value                Position
------------------------------------------------------------
1      <USER>                  <GROUP>                  0
1      IDENTIFIER           main                 4
1      LPAREN               (                    8
...
```

## API Response Format

When using the HTTP API, responses are in JSON format:

```json
{
  "success": true,
  "tokens": [
    {
      "type": "INT",
      "value": "int",
      "line": 1,
      "position": 0
    }
  ],
  "formatted_output": "Lexical Analysis Results:\n...",
  "token_count": 10
}
```

## Files Structure

- `c_lexer.py` - Main lexer implementation
- `main.py` - Command-line interface
- `test_lexer.py` - Test suite with examples
- `web_interface.py` - HTTP API server
- `__init__.py` - Package initialization
- `README.md` - This documentation

## Error Handling

The lexer handles various error conditions:
- Invalid characters are reported with line numbers
- Malformed tokens are skipped with warnings
- API errors return structured error responses

## Integration

This lexer can be easily integrated with:
- Frontend web applications (via HTTP API)
- Other Python projects (via module import)
- Command-line tools and scripts
- IDEs and text editors (as a plugin)
