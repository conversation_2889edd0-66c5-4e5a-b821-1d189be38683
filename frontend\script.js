document.addEventListener('DOMContentLoaded', function() {
    const codeInput = document.getElementById('codeInput');
    const outputDisplay = document.getElementById('outputDisplay');
    const outputType = document.getElementById('outputType');
    const clearBtn = document.getElementById('clearBtn');
    const analyzeBtn = document.getElementById('analyzeBtn');

    // Clear button functionality
    clearBtn.addEventListener('click', function() {
        codeInput.value = '';
        showPlaceholder();
    });

    // Analyze button functionality
    analyzeBtn.addEventListener('click', async function() {
        const code = codeInput.value.trim();
        if (!code) {
            alert('Please enter some C code to analyze');
            return;
        }

        await generateOutput(code, outputType.value);
    });

    // Output type selector change
    outputType.addEventListener('change', async function() {
        const code = codeInput.value.trim();
        if (code) {
            await generateOutput(code, outputType.value);
        }
    });

    function showPlaceholder() {
        outputDisplay.innerHTML = `
            <div class="placeholder">
                <p>Enter C code and click "Analyze Code" to see the results</p>
                <div class="analysis-info">
                    <h3>Analysis Types:</h3>
                    <ul>
                        <li><strong>Lexical Analysis:</strong> Breaks code into tokens (keywords, identifiers, operators, etc.)</li>
                        <li><strong>AST:</strong> Shows the hierarchical structure of your code</li>
                        <li><strong>Flowchart:</strong> Visual representation of program flow</li>
                    </ul>
                    <div style="margin-top: 15px; padding: 10px; background-color: #e8f4fd; border-radius: 5px; border-left: 4px solid #3498db;">
                        <strong>Note:</strong> For full lexical analysis, make sure the lexer server is running.<br>
                        <small>Run: <code>python start_analyzer.py</code> or <code>python lexer/web_interface.py 8001</code></small>
                    </div>
                </div>
            </div>
        `;
    }

    async function generateOutput(code, type) {
        // Show loading message
        outputDisplay.innerHTML = `<div class="output-content">Analyzing code...</div>`;

        let output = '';

        try {
            switch(type) {
                case 'lexical':
                    output = await generateLexicalAnalysis(code);
                    break;
                case 'ast':
                    output = generateAST(code);
                    break;
                case 'flowchart':
                    output = generateFlowchart(code);
                    break;
            }
        } catch (error) {
            output = `Error generating analysis: ${error.message}`;
        }

        outputDisplay.innerHTML = `<div class="output-content">${output}</div>`;
    }

    async function generateLexicalAnalysis(code) {
        try {
            // Call the lexer API
            const response = await fetch('http://localhost:8001/analyze', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ code: code })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                return data.formatted_output;
            } else {
                return `Error during lexical analysis: ${data.error}`;
            }
        } catch (error) {
            // Fallback to local analysis if server is not available
            console.warn('Lexer server not available, using fallback analysis:', error);
            return generateFallbackLexicalAnalysis(code);
        }
    }

    function generateFallbackLexicalAnalysis(code) {
        // Simple fallback lexical analysis for when server is not available
        const lines = code.split('\n');
        let output = `Lexical Analysis Results (Fallback Mode):
==================================================

Input Code:
${code}

Basic Token Analysis:
--------------------
`;

        // Simple pattern matching for basic tokens
        const keywords = ['include', 'int', 'float', 'char', 'double', 'void', 'if', 'else', 'while', 'for', 'return', 'printf', 'scanf', 'main'];
        const operators = ['+', '-', '*', '/', '=', '==', '!=', '<', '>', '<=', '>=', '&&', '||', '!', '++', '--'];
        const delimiters = ['(', ')', '{', '}', '[', ']', ';', ',', '.'];

        let tokenCount = 0;
        let keywordCount = 0;
        let operatorCount = 0;
        let delimiterCount = 0;
        let identifierCount = 0;
        let literalCount = 0;

        lines.forEach((line) => {
            // Remove comments
            line = line.replace(/\/\/.*$/, '').replace(/\/\*.*?\*\//g, '');

            // Simple tokenization
            const tokens = line.match(/\w+|[^\w\s]/g) || [];

            tokens.forEach(token => {
                tokenCount++;
                if (keywords.includes(token.toLowerCase())) {
                    keywordCount++;
                } else if (operators.includes(token)) {
                    operatorCount++;
                } else if (delimiters.includes(token)) {
                    delimiterCount++;
                } else if (/^\d+$/.test(token)) {
                    literalCount++;
                } else if (/^[a-zA-Z_]\w*$/.test(token)) {
                    identifierCount++;
                }
            });
        });

        output += `Total Tokens: ${tokenCount}
Keywords: ${keywordCount}
Identifiers: ${identifierCount}
Operators: ${operatorCount}
Delimiters: ${delimiterCount}
Literals: ${literalCount}

Note: This is a simplified analysis. Start the lexer server for detailed analysis.
To start the server, run: python lexer/web_interface.py 8001`;

        return output;
    }

    function generateAST(code) {
        return `Abstract Syntax Tree (AST):
============================

[This is a placeholder for AST generation]

Input Code:
${code}

AST Structure would be shown here:
Program
├── Include Directive
│   └── stdio.h
├── Function Declaration
│   ├── Return Type: int
│   ├── Name: main
│   ├── Parameters: ()
│   └── Body
│       ├── Variable Declarations
│       ├── Statements
│       └── Return Statement

Note: Actual AST generation logic needs to be implemented.`;
    }

    function generateFlowchart(code) {
        return `Flowchart Representation:
=========================

[This is a placeholder for flowchart generation]

Input Code:
${code}

Flowchart would be displayed here:
┌─────────────┐
│    START    │
└─────┬───────┘
      │
┌─────▼───────┐
│ Include     │
│ Libraries   │
└─────┬───────┘
      │
┌─────▼───────┐
│ Declare     │
│ Variables   │
└─────┬───────┘
      │
┌─────▼───────┐
│ Process     │
│ Logic       │
└─────┬───────┘
      │
┌─────▼───────┐
│    END      │
└─────────────┘

Note: Actual flowchart generation logic needs to be implemented.`;
    }

    // Initialize with placeholder
    showPlaceholder();
});
