document.addEventListener('DOMContentLoaded', function() {
    const codeInput = document.getElementById('codeInput');
    const outputDisplay = document.getElementById('outputDisplay');
    const outputType = document.getElementById('outputType');
    const clearBtn = document.getElementById('clearBtn');
    const analyzeBtn = document.getElementById('analyzeBtn');

    // Clear button functionality
    clearBtn.addEventListener('click', function() {
        codeInput.value = '';
        showPlaceholder();
    });

    // Analyze button functionality
    analyzeBtn.addEventListener('click', function() {
        const code = codeInput.value.trim();
        if (!code) {
            alert('Please enter some C code to analyze');
            return;
        }

        generateOutput(code, outputType.value);
    });

    // Output type selector change
    outputType.addEventListener('change', function() {
        const code = codeInput.value.trim();
        if (code) {
            generateOutput(code, outputType.value);
        }
    });

    function showPlaceholder() {
        outputDisplay.innerHTML = `
            <div class="placeholder">
                <p>Enter C code and click "Analyze Code" to see the results</p>
                <div class="analysis-info">
                    <h3>Analysis Types:</h3>
                    <ul>
                        <li><strong>Lexical Analysis:</strong> Breaks code into tokens (keywords, identifiers, operators, etc.)</li>
                        <li><strong>AST:</strong> Shows the hierarchical structure of your code</li>
                        <li><strong>Flowchart:</strong> Visual representation of program flow</li>
                    </ul>
                    <div style="margin-top: 15px; padding: 10px; background-color: #e8f4fd; border-radius: 5px; border-left: 4px solid #3498db;">
                        <strong>Note:</strong> For full lexical analysis, make sure the lexer server is running.<br>
                        <small>Run: <code>python start_analyzer.py</code> or <code>python lexer/web_interface.py 8001</code></small>
                    </div>
                </div>
            </div>
        `;
    }

    function generateOutput(code, type) {
        // Show loading message
        outputDisplay.innerHTML = `<div class="output-content">Analyzing code...</div>`;

        let output = '';

        try {
            switch(type) {
                case 'lexical':
                    output = generateLexicalAnalysis(code);
                    break;
                case 'ast':
                    output = generateAST(code);
                    break;
                case 'flowchart':
                    output = generateFlowchart(code);
                    break;
            }
        } catch (error) {
            output = `Error generating analysis: ${error.message}`;
        }

        outputDisplay.innerHTML = `<div class="output-content">${output}</div>`;
    }

    function generateLexicalAnalysis(code) {
        return generateSimpleLexicalAnalysis(code);
    }

    function generateSimpleLexicalAnalysis(code) {
        const lines = code.split('\n');
        let output = `Lexical Analysis Results:
==================================================

Input Code:
${code}

Token Analysis:
--------------------
`;

        // C language tokens
        const keywords = [
            'auto', 'break', 'case', 'char', 'const', 'continue', 'default', 'do',
            'double', 'else', 'enum', 'extern', 'float', 'for', 'goto', 'if',
            'int', 'long', 'register', 'return', 'short', 'signed', 'sizeof',
            'static', 'struct', 'switch', 'typedef', 'union', 'unsigned',
            'void', 'volatile', 'while', 'include', 'define', 'printf', 'scanf', 'main'
        ];

        const operators = [
            '+', '-', '*', '/', '%', '=', '==', '!=', '<', '>', '<=', '>=',
            '&&', '||', '!', '&', '|', '^', '~', '<<', '>>', '++', '--',
            '+=', '-=', '*=', '/=', '%=', '->'
        ];

        const delimiters = ['(', ')', '{', '}', '[', ']', ';', ',', '.', ':', '?', '#'];

        let tokens = [];
        let tokenCounts = {
            keywords: 0,
            identifiers: 0,
            operators: 0,
            delimiters: 0,
            literals: 0,
            strings: 0,
            comments: 0,
            preprocessor: 0
        };

        lines.forEach((line, lineNum) => {
            let currentLine = line;
            let position = 0;

            // Handle comments
            const singleLineComment = currentLine.match(/\/\/.*$/);
            if (singleLineComment) {
                tokens.push({
                    type: 'COMMENT',
                    value: singleLineComment[0],
                    line: lineNum + 1,
                    position: currentLine.indexOf(singleLineComment[0])
                });
                tokenCounts.comments++;
                currentLine = currentLine.replace(/\/\/.*$/, '');
            }

            // Handle multi-line comments (simplified)
            const multiLineComment = currentLine.match(/\/\*.*?\*\//g);
            if (multiLineComment) {
                multiLineComment.forEach(comment => {
                    tokens.push({
                        type: 'MULTILINE_COMMENT',
                        value: comment,
                        line: lineNum + 1,
                        position: currentLine.indexOf(comment)
                    });
                    tokenCounts.comments++;
                    currentLine = currentLine.replace(comment, ' ');
                });
            }

            // Handle preprocessor directives
            if (currentLine.trim().startsWith('#')) {
                const preprocessor = currentLine.match(/#\w+/);
                if (preprocessor) {
                    tokens.push({
                        type: 'PREPROCESSOR',
                        value: preprocessor[0],
                        line: lineNum + 1,
                        position: currentLine.indexOf(preprocessor[0])
                    });
                    tokenCounts.preprocessor++;
                }
            }

            // Handle string literals
            const strings = currentLine.match(/"([^"\\]|\\.)*"/g);
            if (strings) {
                strings.forEach(str => {
                    tokens.push({
                        type: 'STRING_LITERAL',
                        value: str,
                        line: lineNum + 1,
                        position: currentLine.indexOf(str)
                    });
                    tokenCounts.strings++;
                    currentLine = currentLine.replace(str, ' ');
                });
            }

            // Handle character literals
            const chars = currentLine.match(/'([^'\\]|\\.)*'/g);
            if (chars) {
                chars.forEach(char => {
                    tokens.push({
                        type: 'CHAR_LITERAL',
                        value: char,
                        line: lineNum + 1,
                        position: currentLine.indexOf(char)
                    });
                    tokenCounts.literals++;
                    currentLine = currentLine.replace(char, ' ');
                });
            }

            // Tokenize remaining content
            const remainingTokens = currentLine.match(/\w+|[^\w\s]/g) || [];

            remainingTokens.forEach(token => {
                if (keywords.includes(token.toLowerCase())) {
                    tokens.push({
                        type: 'KEYWORD',
                        value: token,
                        line: lineNum + 1,
                        position: currentLine.indexOf(token, position)
                    });
                    tokenCounts.keywords++;
                } else if (operators.includes(token)) {
                    tokens.push({
                        type: 'OPERATOR',
                        value: token,
                        line: lineNum + 1,
                        position: currentLine.indexOf(token, position)
                    });
                    tokenCounts.operators++;
                } else if (delimiters.includes(token)) {
                    tokens.push({
                        type: 'DELIMITER',
                        value: token,
                        line: lineNum + 1,
                        position: currentLine.indexOf(token, position)
                    });
                    tokenCounts.delimiters++;
                } else if (/^\d+(\.\d+)?$/.test(token)) {
                    tokens.push({
                        type: 'NUMBER_LITERAL',
                        value: token,
                        line: lineNum + 1,
                        position: currentLine.indexOf(token, position)
                    });
                    tokenCounts.literals++;
                } else if (/^[a-zA-Z_]\w*$/.test(token)) {
                    tokens.push({
                        type: 'IDENTIFIER',
                        value: token,
                        line: lineNum + 1,
                        position: currentLine.indexOf(token, position)
                    });
                    tokenCounts.identifiers++;
                }
                position = currentLine.indexOf(token, position) + token.length;
            });
        });

        // Generate summary
        const totalTokens = tokens.length;
        output += `Total Tokens: ${totalTokens}
Keywords: ${tokenCounts.keywords}
Identifiers: ${tokenCounts.identifiers}
Operators: ${tokenCounts.operators}
Delimiters: ${tokenCounts.delimiters}
Literals: ${tokenCounts.literals}
Strings: ${tokenCounts.strings}
Comments: ${tokenCounts.comments}
Preprocessor: ${tokenCounts.preprocessor}

Detailed Token List:
------------------------------
Line   Type                 Value                Position
------------------------------------------------------------
`;

        // Add detailed token list (limit to first 50 tokens for readability)
        const displayTokens = tokens.slice(0, 50);
        displayTokens.forEach(token => {
            const value = token.value.length > 15 ? token.value.substring(0, 12) + '...' : token.value;
            output += `${token.line.toString().padEnd(6)} ${token.type.padEnd(20)} ${value.padEnd(20)} ${token.position}\n`;
        });

        if (tokens.length > 50) {
            output += `\n... and ${tokens.length - 50} more tokens\n`;
        }

        return output;
    }

    function generateAST(code) {
        return `Abstract Syntax Tree (AST):
============================

[This is a placeholder for AST generation]

Input Code:
${code}

AST Structure would be shown here:
Program
├── Include Directive
│   └── stdio.h
├── Function Declaration
│   ├── Return Type: int
│   ├── Name: main
│   ├── Parameters: ()
│   └── Body
│       ├── Variable Declarations
│       ├── Statements
│       └── Return Statement

Note: Actual AST generation logic needs to be implemented.`;
    }

    function generateFlowchart(code) {
        return `Flowchart Representation:
=========================

[This is a placeholder for flowchart generation]

Input Code:
${code}

Flowchart would be displayed here:
┌─────────────┐
│    START    │
└─────┬───────┘
      │
┌─────▼───────┐
│ Include     │
│ Libraries   │
└─────┬───────┘
      │
┌─────▼───────┐
│ Declare     │
│ Variables   │
└─────┬───────┘
      │
┌─────▼───────┐
│ Process     │
│ Logic       │
└─────┬───────┘
      │
┌─────▼───────┐
│    END      │
└─────────────┘

Note: Actual flowchart generation logic needs to be implemented.`;
    }

    // Initialize with placeholder
    showPlaceholder();
});
